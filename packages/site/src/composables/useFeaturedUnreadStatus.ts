import type { Featured } from '#shared/types'

export function useFeaturedUnreadStatus() {
  const { account } = useAuth()
  const hasUnreadFeatured = ref(false)

  const channel = computed(() =>
    account.value ? `timeline:featured:${toFullHandle(account.value.acct)}` : undefined
  )

  const { stream } = useStreaming<{ type: string, item: Featured }>(channel)

  const checkUnreadStatus = async () => {
    if (!account.value) {
      hasUnreadFeatured.value = false
      return
    }

    try {
      const response = await $fetch<{ items: Featured[] }>('/api/timelines/featured')
      hasUnreadFeatured.value = response.items.some(item => (item.unreadCount ?? 0) > 0)
    } catch (error) {
      console.error('Failed to check featured unread status:', error)
      hasUnreadFeatured.value = false
    }
  }

  // Check status on mount and when account changes
  watchEffect(() => {
    if (account.value) {
      checkUnreadStatus()
    }
  })

  // Update status when stream updates
  watch(stream, (update) => {
    if (update) {
      checkUnreadStatus()
    }
  })

  return {
    hasUnreadFeatured: readonly(hasUnreadFeatured),
    checkUnreadStatus
  }
}