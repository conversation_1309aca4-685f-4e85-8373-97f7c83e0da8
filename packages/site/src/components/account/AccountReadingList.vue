<script setup lang="ts">
import type { Featured, mastodon } from '#shared/types'

const props = defineProps<{
  account: mastodon.v1.Account
}>()

const channel = `timeline:featured:${toFullHandle(props.account.acct)}`
const { stream } = useStreaming<{ type: string, item: Featured }>(computed(() => channel))
const { items, status, error, loadNext } = await usePaginatorFetch<Featured>(
  channel,
  '/api/timelines/featured',
  'id',
  computed(() => ({ type: stream.value?.type, payload: stream.value?.item })),
)
const endAnchor = useEndAnchor(loadNext)

const { markAsRead } = useFeaturedMarkRead()

async function handleItemClick(item: Featured) {
  await markAsRead(item.id)
  // Update the local item to reflect it's been read
  const index = items.value.findIndex(i => i.id === item.id)
  if (index !== -1) {
    items.value[index] = {
      ...items.value[index],
      readDate: new Date().toISOString(),
      unreadCount: 0
    }
  }
}
</script>

<template>
  <ul class="account-reading-list">
    <CommonPaginator :items="items" v-bind="{ keyProp: 'id', pending: status === 'pending', error, loadNext }">
      <template #default="{ item }">
        <AccountReadingPersonCard v-if="item.account" :account="item.account" :unread-count="item.unreadCount" @click="handleItemClick(item)" />
        <AccountReadingTagCard v-if="item.tag" :tag="item.tag" :unread-count="item.unreadCount" @click="handleItemClick(item)" />
      </template>
      <template #done>
        <div ref="endAnchor" />
      </template>
    </CommonPaginator>
  </ul>
</template>

<style>
.account-reading-list {
  border-radius: var(--corner-radius);
  /* max-height: calc(100svh - var(--padding-base)* 2 - var(--base-size) - var(--padding-big)); */
}
</style>
