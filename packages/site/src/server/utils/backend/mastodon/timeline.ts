import { PUBLIC_GROUP } from '../activitypub/activities'
import { propertiesSymbol as actorPropertiesSymbol, actorURL, personFromRow, type Actor } from '../activitypub/actors'
import { toObjectFromRow, type APObject } from '../activitypub/objects'
import { NOTE } from '../activitypub/objects/note'
import type { Cache } from '../cache'
import { qb } from '../database/querybuilder'
import { getLocalAccounts, toMastodonAccount } from './account'
import { STATE_ACCEPTED } from './follow'
import { getTags } from './hashtag'
import { toMastodonStatusFromRow } from './status'
import type { mastodon } from '#shared/types'

export async function pregenerateTimelines(domain: string, db: D1Database, cache: Cache, actor: Actor): Promise<Array<mastodon.v1.Status>> {
  const timeline = await getHomeTimeline(domain, db, actor)
  await cache.put(actor.id + '/timeline/home', timeline)
  return timeline
}

export async function getHomeTimeline(domain: string, db: D1Database, actor: Actor): Promise<Array<mastodon.v1.Status>> {
  const following = await getActorFollowing(db, actor.id.toString())
  const followingIds = following.map(row => row.id)
  const followingFollowersURLs = following.map(row => row.actorFollowersURL)

  // follow ourself to see our statuses in the our home timeline
  followingIds.push(actor.id.toString())

  const QUERY = `
SELECT objects.*,
       actors.id as actor_id,
       actors.cdate as actor_cdate,
       actors.properties as actor_properties,
       outbox_objects.actor_id as publisher_actor_id,
       (SELECT count(*) FROM actor_favourites WHERE actor_favourites.object_id=objects.id) as favourites_count,
       (SELECT count(*) FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id) as reblogs_count,
       (SELECT count(*) FROM actor_replies WHERE actor_replies.in_reply_to_object_id=objects.id) as replies_count,
       (SELECT count(*) > 0 FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id AND actor_reblogs.actor_id=?1) as reblogged,
       (SELECT count(*) > 0 FROM actor_favourites WHERE actor_favourites.object_id=objects.id AND actor_favourites.actor_id=?1) as favourited
FROM outbox_objects
INNER JOIN objects ON objects.id = outbox_objects.object_id
INNER JOIN actors ON actors.id = outbox_objects.actor_id
WHERE
     objects.type = '${NOTE}'
     AND outbox_objects.actor_id IN ${qb.set('?2')}
     AND ${qb.jsonExtractIsNull('objects.properties', 'inReplyTo')}
     AND (outbox_objects.target = '${PUBLIC_GROUP}' OR outbox_objects.target IN ${qb.set('?3')})
GROUP BY objects.id
ORDER by outbox_objects.published_date DESC
LIMIT ?4
`
  const DEFAULT_LIMIT = 20

  const { success, error, results } = await db
    .prepare(QUERY)
    .bind(actor.id.toString(), JSON.stringify(followingIds), JSON.stringify(followingFollowersURLs), DEFAULT_LIMIT)
    .all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }

  const out: Array<mastodon.v1.Status> = []

  for (let i = 0, len = results.length; i < len; i++) {
    const status = await toMastodonStatusFromRow(domain, db, results[i])
    if (status !== null) {
      out.push(status)
    }
  }

  return out
}

export enum LocalPreference {
  NotSet,
  OnlyLocal,
  OnlyRemote,
}

function localPreferenceQuery(preference: LocalPreference): string {
  switch (preference) {
    case LocalPreference.NotSet:
      return 'true'
    case LocalPreference.OnlyLocal:
      return 'objects.local = 1'
    case LocalPreference.OnlyRemote:
      return 'objects.local = 0'
  }
}

export async function getPublicTimeline(
  domain: string,
  db: D1Database,
  localPreference: LocalPreference,
  offset: number = 0,
  hashtag?: string,
): Promise<Array<mastodon.v1.Status>> {
  let hashtagJoin = ''
  let hashtagFilter = ''
  if (hashtag) {
    hashtagJoin = 'LEFT JOIN note_tags ON objects.id=note_tags.object_id'
    hashtagFilter = 'AND note_tags.tag_id=?3'
  }

  const QUERY = `
SELECT objects.*,
       actors.id as actor_id,
       actors.cdate as actor_cdate,
       actors.properties as actor_properties,
       outbox_objects.actor_id as publisher_actor_id,
       (SELECT count(*) FROM actor_favourites WHERE actor_favourites.object_id=objects.id) as favourites_count,
       (SELECT count(*) FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id) as reblogs_count,
       (SELECT count(*) FROM actor_replies WHERE actor_replies.in_reply_to_object_id=objects.id) as replies_count
FROM outbox_objects
INNER JOIN objects ON objects.id=outbox_objects.object_id
INNER JOIN actors ON actors.id=outbox_objects.actor_id
${hashtagJoin}
WHERE objects.type='${NOTE}'
      AND ${localPreferenceQuery(localPreference)}
      AND ${qb.jsonExtractIsNull('objects.properties', 'inReplyTo')}
      AND outbox_objects.target = '${PUBLIC_GROUP}'
      ${hashtagFilter}
GROUP BY objects.id
ORDER by outbox_objects.published_date DESC
LIMIT ?1 OFFSET ?2
`
  const DEFAULT_LIMIT = 20

  let query = db.prepare(QUERY)
  if (hashtagFilter) {
    query = query.bind(DEFAULT_LIMIT, offset, hashtag)
  }
  else {
    query = query.bind(DEFAULT_LIMIT, offset)
  }

  const { success, error, results } = await query.all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }

  const out: Array<mastodon.v1.Status> = []

  for (let i = 0, len = results.length; i < len; i++) {
    const status = await toMastodonStatusFromRow(domain, db, results[i])
    if (status !== null) {
      out.push(status)
    }
  }

  return out
}

export async function getTagTimeline(domain: string, db: D1Database, hashtag: string, offset: number = 0, actor?: Actor | null): Promise<Array<mastodon.v1.Status>> {
  let actorInfo = ''
  const targets = [PUBLIC_GROUP]
  if (actor) {
    actorInfo = `
    (SELECT count(*) > 0 FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id AND actor_reblogs.actor_id=?4) as reblogged,
    (SELECT count(*) > 0 FROM actor_favourites WHERE actor_favourites.object_id=objects.id AND actor_favourites.actor_id=?4) as favourited,
    `
    const following = await getActorFollowing(db, actor.id.toString())
    for (const { actorFollowersURL } of following) {
      targets.push(actorFollowersURL)
    }
  }

  const query = `
SELECT objects.*,
       actors.id as actor_id,
       actors.cdate as actor_cdate,
       actors.properties as actor_properties,
       outbox_objects.actor_id as publisher_actor_id,
       ${actorInfo}
       (SELECT count(*) FROM actor_favourites WHERE actor_favourites.object_id=objects.id) as favourites_count,
       (SELECT count(*) FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id) as reblogs_count,
       (SELECT count(*) FROM actor_replies WHERE actor_replies.in_reply_to_object_id=objects.id) as replies_count
FROM outbox_objects
INNER JOIN objects ON objects.id=outbox_objects.object_id
INNER JOIN actors ON actors.id=outbox_objects.actor_id
INNER JOIN note_tags ON objects.id=note_tags.object_id
WHERE objects.type='${NOTE}'
      AND json_extract(objects.properties, '$.inReplyTo') IS NULL
      AND outbox_objects.target IN (SELECT value FROM json_each(?3))
      AND note_tags.tag_id=?2
GROUP BY objects.id
ORDER by outbox_objects.published_date DESC
LIMIT 20 OFFSET ?1
`
  let stmt = db.prepare(query)
  if (actor) {
    stmt = stmt.bind(offset, hashtag, JSON.stringify(targets), actor.id.toString())
  }
  else {
    stmt = stmt.bind(offset, hashtag, JSON.stringify(targets))
  }
  const { success, error, results } = await stmt.all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }
  const out: Array<mastodon.v1.Status> = []
  for (const result of results) {
    const status = await toMastodonStatusFromRow(domain, db, result)
    if (status !== null) {
      out.push(status)
    }
  }
  return out
}

export async function pregenerateActorsTimeline(db: D1Database, cache: Cache, domain: string) {
  const timeline = await getActorsTimeline(db, domain)
  await cache.put('/timeline/actors', timeline)
}

export async function fetchActorsTimeline(db: D1Database/* , cache: Cache */, domain: string) {
  // return cache.fetch('/timeline/actors', () => getActorsTimeline(db))
  return getActorsTimeline(db, domain)
}

async function getActorsTimeline(db: D1Database, domain: string) {
  const sql_published = `
    SELECT outbox_objects.actor_id
      FROM outbox_objects
INNER JOIN objects ON objects.id = outbox_objects.object_id
     WHERE objects.type = '${NOTE}'
       AND objects.local = 1
       AND json_extract(objects.properties, '$.inReplyTo') IS NULL
       AND outbox_objects.target = '${PUBLIC_GROUP}'
  GROUP BY outbox_objects.id
  ORDER BY outbox_objects.published_date DESC
     LIMIT 20
`
  const sql_statuses = `
    SELECT count(*)
      FROM outbox_objects
INNER JOIN objects ON objects.id = outbox_objects.object_id
     WHERE outbox_objects.actor_id = actors.id
       AND outbox_objects.target = '${PUBLIC_GROUP}'
       AND objects.type = '${NOTE}'
       AND json_extract(objects.properties, '$.inReplyTo') IS NULL
`
  const sql_following = `
    SELECT count(*)
      FROM actor_following_actors
     WHERE actor_following_actors.actor_id = actors.id
`
  const sql_followers = `
    SELECT count(*)
      FROM actor_following_actors
     WHERE actor_following_actors.target_actor_id = actors.id
`
  const sql = `
SELECT actors.*,
(${sql_statuses}) AS statuses_count,
(${sql_following}) AS following_count,
(${sql_followers}) AS followers_count
FROM actors
WHERE actors.id IN (${sql_published})
`
  console.debug(sql)
  const stmt = db.prepare(sql)
  const { success, error, results } = await stmt.all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }
  const accounts: mastodon.v1.Account[] = []
  for (const row of results) {
    const actor = personFromRow(row)
    const account = toMastodonAccount(actor.preferredUsername || 'unknown', actor)
    account.statusesCount = Number(row.statuses_count)
    account.followersCount = Number(row.followers_count)
    account.followingCount = Number(row.following_count)
    if (actor[actorPropertiesSymbol].tags?.length) {
      account.tags = await getTags(db, domain, { ids: actor[actorPropertiesSymbol].tags })
    }
    accounts.push(account)
  }
  return accounts
}

export async function getTagActorsTimeline(db: D1Database, tagId: string, domain: string) {
  const sql = `
SELECT actors.*,
(SELECT count(*) FROM outbox_objects INNER JOIN objects ON objects.id = outbox_objects.object_id WHERE outbox_objects.actor_id = actors.id AND objects.type = '${NOTE}' AND json_extract(objects.properties, '$.inReplyTo') IS NULL) AS statuses_count,
(SELECT count(*) FROM actor_following_actors WHERE actor_following_actors.actor_id = actors.id) AS following_count,
(SELECT count(*) FROM actor_following_actors WHERE actor_following_actors.target_actor_id = actors.id) AS followers_count
FROM actors
WHERE EXISTS (SELECT 1 FROM json_each(properties, '$.tags') WHERE value = ?)
LIMIT 20
`
  const stmt = db.prepare(sql).bind(tagId)
  const { success, error, results } = await stmt.all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }
  const accounts: mastodon.v1.Account[] = []
  for (const row of results) {
    const actor = personFromRow(row)
    const account = toMastodonAccount(actor.preferredUsername || 'unknown', actor)
    account.statusesCount = Number(row.statuses_count)
    account.followersCount = Number(row.followers_count)
    account.followingCount = Number(row.following_count)
    if (actor[actorPropertiesSymbol]?.tags?.length) {
      account.tags = await getTags(db, domain, { ids: actor[actorPropertiesSymbol].tags })
    }
    accounts.push(account)
  }
  return accounts
}

interface FeaturedRow {
  id: string
  type: string
  targetId: string
  publishedDate: string
  properties: string
}

interface FeaturedProperties {
  readDate?: string
  unreadCount?: number
}

export async function getFeaturedTimeline(db: D1Database, domain: string, actor: Actor, maxId?: string) {
  let maxIdDate: string | undefined
  if (maxId) {
    const sql = `
      SELECT published_date FROM (
        SELECT id, published_date FROM actor_following_actors WHERE actor_id=?1 AND state=?2
        UNION
        SELECT id, published_date FROM actor_following_tags WHERE actor_id=?1
      ) WHERE id = ?3
    `
    const result = await db.prepare(sql).bind(actor.id.toString(), STATE_ACCEPTED, maxId).first<{ published_date: string }>()
    maxIdDate = result?.published_date
  }

  const rules = ['actor_id = ?1']
  const binds = [actor.id.toString(), STATE_ACCEPTED]
  if (maxIdDate) {
    rules.push('published_date < ?3')
    binds.push(maxIdDate)
  }

  const sql = `
    SELECT id, 'actor' AS type, target_actor_id AS targetId, published_date AS publishedDate, properties
    FROM actor_following_actors
    WHERE ${[...rules, 'state = ?2'].join(' AND ')}
    UNION
    SELECT id, 'tag' AS type, tag_id AS targetId, published_date AS publishedDate, properties
    FROM actor_following_tags
    WHERE ${rules.join(' AND ')}
    ORDER BY published_date DESC
    LIMIT 20
  `

  const { success, error, results } = await db.prepare(sql).bind(...binds).all<FeaturedRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }
  const actorIds = results.filter(({ type }) => type === 'actor').map(({ targetId }) => targetId)
  const accounts: Record<string, mastodon.v1.Account> = (await getLocalAccounts(db, { ids: actorIds })).reduce((result, item) => ({ ...result, [actorURL(domain, item.username).toString()]: item }), {})
  const tagIds = results.filter(({ type }) => type === 'tag').map(({ targetId }) => targetId)
  const tags: Record<string, mastodon.v1.Tag> = (await getTags(db, domain, { ids: tagIds, history: true })).reduce((result, item) => ({ ...result, [item.id]: item }), {})

  // Calculate unread counts
  const unreadCounts: Record<string, number> = {}
  for (const result of results) {
    const props = JSON.parse(result.properties) as FeaturedProperties
    const readDate = props.readDate || '1970-01-01T00:00:00.000Z'

    let countSql = ''
    if (result.type === 'actor') {
      countSql = `
        SELECT COUNT(*) as count
        FROM outbox_objects
        INNER JOIN objects ON objects.id = outbox_objects.object_id
        WHERE outbox_objects.actor_id = ?
        AND objects.type = '${NOTE}'
        AND outbox_objects.published_date > ?
        AND outbox_objects.target = '${PUBLIC_GROUP}'
      `
      const countResult = await db.prepare(countSql).bind(result.targetId, readDate).first<{ count: number }>()
      unreadCounts[result.id] = countResult?.count || 0
    } else if (result.type === 'tag') {
      countSql = `
        SELECT COUNT(*) as count
        FROM note_tags
        INNER JOIN outbox_objects ON outbox_objects.object_id = note_tags.object_id
        WHERE note_tags.tag_id = ?
        AND outbox_objects.published_date > ?
        AND outbox_objects.target = '${PUBLIC_GROUP}'
      `
      const countResult = await db.prepare(countSql).bind(result.targetId, readDate).first<{ count: number }>()
      unreadCounts[result.id] = countResult?.count || 0
    }
  }

  return results.map(result => ({
    id: result.id,
    publishedDate: result.publishedDate,
    readDate: JSON.parse(result.properties).readDate,
    unreadCount: unreadCounts[result.id] || 0,
    account: result.type === 'actor' ? accounts[result.targetId] : undefined,
    tag: result.type === 'tag' ? tags[result.targetId] : undefined,
  }))
}

export async function getActorTimeline(db: D1Database, actor: Actor): Promise<Array<APObject>> {
  const QUERY = `
  SELECT objects.*
    FROM outbox_objects JOIN objects ON objects.id = outbox_objects.object_id
   WHERE objects.type = '${NOTE}'
     AND objects.local = 1
     AND outbox_objects.target = '${PUBLIC_GROUP}'
     AND outbox_objects.actor_id = ?
ORDER BY outbox_objects.published_date DESC
   LIMIT 10
`
  const stmt = db.prepare(QUERY).bind(actor.id.toString())
  const { success, error, results } = await stmt.all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }
  return results.map(toObjectFromRow)
}

async function getActorFollowing(db: D1Database, actorId: string): Promise<{ id: string, actorFollowersURL: string }[]> {
  const QUERY = `
        SELECT
        actor_following_actors.target_actor_id as id,
            json_extract(actors.properties, '$.followers') as actorFollowersURL
        FROM actor_following_actors
        INNER JOIN actors ON actors.id = actor_following_actors.target_actor_id
        WHERE actor_id=? AND state='accepted'
    `
  const { results } = await db
    .prepare(QUERY)
    .bind(actorId)
    .all<{ id: string, actorFollowersURL: string | null }>()

  return (results ?? []).map(({ id, actorFollowersURL }) => ({
    id,
    actorFollowersURL: actorFollowersURL || id + '/followers',
  }))
}
